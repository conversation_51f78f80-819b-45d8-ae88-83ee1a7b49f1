/**
 * Validation script for cron job endpoints
 *
 * This script tests all cron job endpoints to ensure they are properly configured
 * and working with the referral system and flat subscription architecture.
 *
 * Usage:
 * npm run validate:cron-jobs
 * npm run validate:cron-jobs .env.production
 * ENV_FILE=.env.staging npm run validate:cron-jobs
 */

import { config } from "dotenv"
import { Command } from "commander"

// Load environment variables
const program = new Command()
program.argument("[env-file]", "Environment file to use", ".env")
program.parse()

const envFile = program.args[0] || process.env.ENV_FILE || ".env"
config({ path: envFile })

console.log(`🔧 Using environment file: ${envFile}`)

interface CronEndpoint {
  name: string
  path: string
  method: "GET" | "POST"
  description: string
}

const CRON_ENDPOINTS: CronEndpoint[] = [
  {
    name: "Trip Activation",
    path: "/api/cron/activate-trips",
    method: "GET",
    description: "Activate trips that are scheduled to start",
  },
  {
    name: "Trip Completion",
    path: "/api/cron/complete-trips",
    method: "GET",
    description: "Complete trips that have ended",
  },
  {
    name: "Subscription Expiration",
    path: "/api/cron/subscription-expiration",
    method: "POST",
    description: "Process expired subscription entries",
  },
  {
    name: "Perk Expiration",
    path: "/api/cron/perk-expiration",
    method: "POST",
    description: "Process expired perks",
  },
  {
    name: "Subscription Cleanup",
    path: "/api/cron/subscription-cleanup",
    method: "POST",
    description: "Clean up old expired subscription entries",
  },
  {
    name: "Subscription Health",
    path: "/api/cron/subscription-health",
    method: "POST",
    description: "Monitor subscription system health",
  },
  {
    name: "Referral Health",
    path: "/api/cron/referral-health",
    method: "POST",
    description: "Monitor referral system health",
  },
]

async function validateCronJobs() {
  console.log("🚀 Validating cron job endpoints...")
  console.log("=".repeat(50))

  const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000"
  const cronSecret = process.env.CRON_SECRET

  if (!cronSecret) {
    console.error("❌ CRON_SECRET environment variable is not set")
    process.exit(1)
  }

  console.log(`🌐 Base URL: ${baseUrl}`)
  console.log(`🔑 Using CRON_SECRET: ${cronSecret.substring(0, 8)}...`)
  console.log()

  let successCount = 0
  let failureCount = 0
  const results: Array<{ endpoint: CronEndpoint; success: boolean; error?: string }> = []

  for (const endpoint of CRON_ENDPOINTS) {
    console.log(`🧪 Testing ${endpoint.name}...`)

    try {
      const url = `${baseUrl}${endpoint.path}`
      const response = await fetch(url, {
        method: endpoint.method,
        headers: {
          Authorization: `Bearer ${cronSecret}`,
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        const data = await response.json()
        console.log(`✅ ${endpoint.name}: SUCCESS`)
        console.log(`   Status: ${response.status}`)
        console.log(`   Response: ${data.message || "OK"}`)
        successCount++
        results.push({ endpoint, success: true })
      } else {
        const errorText = await response.text()
        console.log(`❌ ${endpoint.name}: FAILED`)
        console.log(`   Status: ${response.status}`)
        console.log(`   Error: ${errorText}`)
        failureCount++
        results.push({ endpoint, success: false, error: `HTTP ${response.status}: ${errorText}` })
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ERROR`)
      console.log(`   Error: ${error}`)
      failureCount++
      results.push({
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      })
    }

    console.log()
  }

  // Summary
  console.log("📊 VALIDATION SUMMARY")
  console.log("=".repeat(50))
  console.log(`✅ Successful: ${successCount}`)
  console.log(`❌ Failed: ${failureCount}`)
  console.log(`📈 Success Rate: ${((successCount / CRON_ENDPOINTS.length) * 100).toFixed(1)}%`)
  console.log()

  if (failureCount > 0) {
    console.log("❌ FAILED ENDPOINTS:")
    results
      .filter((r) => !r.success)
      .forEach((r) => {
        console.log(`   • ${r.endpoint.name}: ${r.error}`)
      })
    console.log()
  }

  // Health check endpoints
  console.log("🏥 Testing health check endpoints...")
  const healthEndpoints = [
    "/api/cron/subscription-health",
    "/api/cron/referral-health",
    "/api/cron/subscription-expiration",
    "/api/cron/perk-expiration",
  ]

  for (const path of healthEndpoints) {
    try {
      const url = `${baseUrl}${path}`
      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${cronSecret}`,
        },
      })

      if (response.ok) {
        console.log(`✅ Health check ${path}: OK`)
      } else {
        console.log(`⚠️  Health check ${path}: ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ Health check ${path}: ERROR`)
    }
  }

  console.log()

  if (failureCount === 0) {
    console.log("🎉 All cron job endpoints are working correctly!")
    console.log()
    console.log("Next steps:")
    console.log("1. Configure your cron scheduler (GitHub Actions, Vercel Cron, etc.)")
    console.log("2. Set up monitoring and alerting")
    console.log("3. Test the scheduled execution")
  } else {
    console.log("⚠️  Some endpoints failed validation. Please check the configuration.")
    process.exit(1)
  }
}

// Run validation
validateCronJobs().catch((error) => {
  console.error("❌ Validation failed:", error)
  process.exit(1)
})
