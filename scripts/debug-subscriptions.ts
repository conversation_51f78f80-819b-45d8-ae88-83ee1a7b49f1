#!/usr/bin/env tsx

/**
 * Debug script to examine current subscription data structure
 */

import { config } from "dotenv"
import { resolve } from "path"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"

// Parse command line arguments
const args = process.argv.slice(2)
const envFile = args.find((arg) => !arg.startsWith("--")) || ".env.local"

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  const envPath = resolve(process.cwd(), envFile)
  console.log(`Loading environment variables from: ${envPath}`)
  config({ path: envPath })

  if (!getApps().length) {
    try {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      initializeApp({
        credential: cert(serviceAccount),
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      })

      console.log("✅ Firebase Admin SDK initialized successfully")
    } catch (error) {
      console.error("❌ Firebase admin initialization error:", error)
      process.exit(1)
    }
  }

  return getFirestore()
}

async function debugSubscriptions() {
  const db = getFirestore()

  console.log("\n🔍 Examining userSubscriptions collection...\n")

  // Get all documents in userSubscriptions collection
  const subscriptionsSnapshot = await db.collection("userSubscriptions").get()

  console.log(`📊 Total documents in userSubscriptions: ${subscriptionsSnapshot.size}`)

  if (subscriptionsSnapshot.size === 0) {
    console.log("❌ No documents found in userSubscriptions collection")
    return
  }

  // Analyze the structure of each document
  let oldStructureCount = 0
  let newStructureCount = 0
  let stripeSubscriptions = 0
  let freeSubscriptions = 0

  console.log("\n📋 Document Analysis:")
  console.log("=".repeat(80))

  for (const doc of subscriptionsSnapshot.docs) {
    const data = doc.data()

    // Check if it's old structure (has userId field matching doc ID)
    if (doc.id === data.userId) {
      oldStructureCount++
      console.log(`\n🔸 OLD STRUCTURE - Document ID: ${doc.id}`)
      console.log(`   userId: ${data.userId}`)
      console.log(`   subscriptionPlan: ${data.subscriptionPlan}`)
      console.log(`   subscriptionStatus: ${data.subscriptionStatus}`)
      console.log(`   stripeCustomerId: ${data.stripeCustomerId || "N/A"}`)
      console.log(`   subscriptionId: ${data.subscriptionId || "N/A"}`)
      console.log(`   subscriptionCurrentPeriodEnd: ${data.subscriptionCurrentPeriodEnd || "N/A"}`)

      // Check if this should be a Stripe subscription
      if (data.stripeCustomerId && data.subscriptionId && data.subscriptionPlan !== "free") {
        stripeSubscriptions++
        console.log(`   🎯 SHOULD BE STRIPE ENTRY`)
      } else {
        console.log(`   🆓 SHOULD BE FREE ENTRY`)
      }
    }
    // Check if it's new structure (has source field)
    else if (data.source) {
      newStructureCount++
      console.log(`\n🔹 NEW STRUCTURE - Document ID: ${doc.id}`)
      console.log(`   userId: ${data.userId}`)
      console.log(`   source: ${data.source}`)
      console.log(`   status: ${data.status}`)
      console.log(`   precedence: ${data.precedence}`)

      if (data.source === "stripe") {
        stripeSubscriptions++
      } else if (data.source === "free") {
        freeSubscriptions++
      }
    } else {
      console.log(`\n❓ UNKNOWN STRUCTURE - Document ID: ${doc.id}`)
      console.log(`   Data keys: ${Object.keys(data).join(", ")}`)
    }
  }

  console.log("\n📊 Summary:")
  console.log("=".repeat(50))
  console.log(`Old structure documents: ${oldStructureCount}`)
  console.log(`New structure documents: ${newStructureCount}`)
  console.log(`Documents with Stripe data: ${stripeSubscriptions}`)
  console.log(`Free subscription documents: ${freeSubscriptions}`)

  // Check users collection
  console.log("\n👥 Checking users collection...")
  const usersSnapshot = await db.collection("users").get()
  console.log(`Total users: ${usersSnapshot.size}`)
  console.log(
    `Users without subscription entries: ${usersSnapshot.size - subscriptionsSnapshot.size}`
  )

  // List users without subscriptions
  const userIdsWithSubscriptions = new Set()
  for (const doc of subscriptionsSnapshot.docs) {
    const data = doc.data()
    if (data.userId) {
      userIdsWithSubscriptions.add(data.userId)
    } else if (doc.id.length > 10) {
      // Likely a user ID if it's long
      userIdsWithSubscriptions.add(doc.id)
    }
  }

  console.log("\n👤 Users without subscription entries:")
  let usersWithoutSubs = 0
  for (const userDoc of usersSnapshot.docs) {
    if (!userIdsWithSubscriptions.has(userDoc.id)) {
      usersWithoutSubs++
      const userData = userDoc.data()
      console.log(`   - ${userDoc.id} (${userData.email || "No email"})`)
    }
  }

  if (usersWithoutSubs === 0) {
    console.log("   ✅ All users have subscription entries")
  }
}

async function main() {
  try {
    await initializeFirebase()
    await debugSubscriptions()
  } catch (error) {
    console.error("❌ Debug failed:", error)
    process.exit(1)
  }
}

main().catch((error) => {
  console.error("💥 Script failed:", error)
  process.exit(1)
})
