# Cron Jobs Configuration for Togeda.ai

## Overview

This document outlines all cron jobs required for the proper functioning of Togeda.ai's referral system and flat subscription architecture.

## Required Environment Variables

Ensure these environment variables are set in production:

```bash
CRON_SECRET=<secure-random-string>
FIREBASE_SERVICE_ACCOUNT_KEY=<service-account-json>
NEXT_PUBLIC_FIREBASE_PROJECT_ID=<project-id>
```

## Cron Job Endpoints

### 1. Subscription Expiration Processing (Daily)

**Endpoint**: `POST /api/cron/subscription-expiration`
**Schedule**: Daily at 2:00 AM UTC
**Purpose**: Process expired subscription entries and activate next highest precedence subscriptions

```bash
# Example cron schedule (GitHub Actions)
cron: '0 2 * * *'

# Example curl command
curl -X POST https://your-domain.com/api/cron/subscription-expiration \
  -H "Authorization: Bearer $CRON_SECRET"
```

**What it does**:

- Marks expired subscription entries as "expired"
- Activates next highest precedence subscription (Stripe, perk, free)
- Handles perk subscription expiration with proper logging
- Processes pause/resume logic for Stripe subscriptions

### 2. Perk Expiration Processing (Daily)

**Endpoint**: `POST /api/cron/perk-expiration`
**Schedule**: Daily at 3:00 AM UTC
**Purpose**: Process expired perks in the users/{userId}/perks collection

```bash
# Example cron schedule (GitHub Actions)
cron: '0 3 * * *'

# Example curl command
curl -X POST https://your-domain.com/api/cron/perk-expiration \
  -H "Authorization: Bearer $CRON_SECRET"
```

**What it does**:

- Checks all user perks for expiration
- Marks expired perks as "expired" status
- Processes perks in batches to avoid performance issues
- Logs detailed expiration information

### 3. Subscription Cleanup (Weekly)

**Endpoint**: `POST /api/cron/subscription-cleanup`
**Schedule**: Weekly on Sunday at 4:00 AM UTC
**Purpose**: Clean up old expired subscription entries

```bash
# Example cron schedule (GitHub Actions)
cron: '0 4 * * 0'

# Example curl command
curl -X POST https://your-domain.com/api/cron/subscription-cleanup \
  -H "Authorization: Bearer $CRON_SECRET"
```

**What it does**:

- Removes expired subscription entries older than 30 days
- Helps maintain database performance
- Preserves recent expired entries for audit purposes

### 4. Subscription Health Check (Every 6 Hours)

**Endpoint**: `POST /api/cron/subscription-health`
**Schedule**: Every 6 hours
**Purpose**: Monitor subscription system health

```bash
# Example cron schedule (GitHub Actions)
cron: '0 */6 * * *'

# Example curl command
curl -X POST https://your-domain.com/api/cron/subscription-health \
  -H "Authorization: Bearer $CRON_SECRET"
```

**What it does**:

- Checks for users with multiple applied subscriptions
- Validates subscription precedence logic
- Monitors for missing free subscriptions
- Returns system health status (healthy/warning/critical)

### 5. Referral System Health Check (Every 6 Hours)

**Endpoint**: `POST /api/cron/referral-health`
**Schedule**: Every 6 hours (offset by 3 hours from subscription health)
**Purpose**: Monitor referral system and perk integrity

```bash
# Example cron schedule (GitHub Actions)
cron: '0 3,9,15,21 * * *'

# Example curl command
curl -X POST https://your-domain.com/api/cron/referral-health \
  -H "Authorization: Bearer $CRON_SECRET"
```

**What it does**:

- Validates referral code integrity
- Checks for missing eligible perks
- Monitors perk expiration status
- Validates referral count consistency

### 6. Trip Activation (Daily)

**Endpoint**: `GET /api/cron/activate-trips`
**Schedule**: Daily at 1:00 AM UTC
**Purpose**: Activate trips that are scheduled to start

```bash
# Example cron schedule (GitHub Actions)
cron: '0 1 * * *'

# Example curl command
curl -X GET https://your-domain.com/api/cron/activate-trips \
  -H "Authorization: Bearer $CRON_SECRET"
```

### 7. Trip Completion (Daily)

**Endpoint**: `GET /api/cron/complete-trips`
**Schedule**: Daily at 23:00 UTC
**Purpose**: Complete trips that have ended

```bash
# Example cron schedule (GitHub Actions)
cron: '0 23 * * *'

# Example curl command
curl -X GET https://your-domain.com/api/cron/complete-trips \
  -H "Authorization: Bearer $CRON_SECRET"
```

## GitHub Actions Configuration

Create `.github/workflows/cron-jobs.yml`:

```yaml
name: Cron Jobs

on:
  schedule:
    # Trip activation - 1:00 AM UTC
    - cron: "0 1 * * *"
    # Subscription expiration - 2:00 AM UTC
    - cron: "0 2 * * *"
    # Perk expiration - 3:00 AM UTC
    - cron: "0 3 * * *"
    # Health checks - every 6 hours
    - cron: "0 */6 * * *"
    # Cleanup - weekly on Sunday 4:00 AM UTC
    - cron: "0 4 * * 0"
    # Trip completion - 23:00 UTC
    - cron: "0 23 * * *"

jobs:
  cron-jobs:
    runs-on: ubuntu-latest
    steps:
      - name: Run Cron Jobs
        run: |
          # Add your cron job execution logic here
          # This will depend on your deployment platform
```

## Monitoring and Alerting

### Health Check Responses

All health check endpoints return status codes that can be used for monitoring:

- `200`: Healthy system
- `500`: Critical issues detected
- `401`: Authentication failure

### Logging

All cron jobs log their execution to:

- `cronLogs` collection (subscription-related jobs)
- `referralHealthLogs` collection (referral health checks)

### Recommended Monitoring

1. **Response Time**: Monitor cron job execution time
2. **Success Rate**: Track successful vs failed executions
3. **System Health**: Monitor health check status codes
4. **Error Rates**: Track error counts and types

## Troubleshooting

### Common Issues

1. **Authentication Failures**

   - Verify `CRON_SECRET` environment variable
   - Check authorization header format

2. **Database Connection Issues**

   - Verify Firebase service account credentials
   - Check Firestore security rules

3. **Performance Issues**
   - Monitor batch processing sizes
   - Check for database query optimization

### Manual Execution

All cron endpoints support manual execution for testing:

```bash
# Manual health check
curl -X GET https://your-domain.com/api/cron/subscription-health \
  -H "Authorization: Bearer $CRON_SECRET"

# Manual referral health check
curl -X GET https://your-domain.com/api/cron/referral-health \
  -H "Authorization: Bearer $CRON_SECRET"
```

## Security Considerations

1. **Authentication**: All endpoints require `CRON_SECRET` authorization
2. **Rate Limiting**: Consider implementing rate limiting for manual endpoints
3. **Logging**: Avoid logging sensitive information
4. **Error Handling**: Graceful error handling to prevent system disruption

## Validation Script

Use the validation script to test all cron endpoints:

```bash
# Run validation script
npm run validate:cron-jobs

# Or with custom environment
ENV_FILE=.env.production npm run validate:cron-jobs
```

The validation script will:

- Test all cron endpoints for connectivity
- Verify authentication is working
- Check response formats
- Validate health check endpoints

---

This configuration ensures proper maintenance of both the referral system and flat subscription architecture with comprehensive monitoring and health checks.
